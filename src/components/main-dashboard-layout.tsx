"use client";

import { DashboardHeader } from "@/components/dashboard-header";
import { DashboardSidebar } from "@/components/dashboard-sidebar";
import { useProjectStore } from "@/store/projectStore";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Layout, Spin } from "antd";

const { Header, Sider, Content } = Layout;

interface MainDashboardLayoutProps {
  projectId: string;
  children: React.ReactNode;
}

export function MainDashboardLayout({ projectId, children }: MainDashboardLayoutProps) {
  const { projects, selectedProjectId, selectProject, fetchProjects, isLoading, error } = useProjectStore();
  const router = useRouter();

  useEffect(() => {
    if (projectId && !selectedProjectId) {
      selectProject(projectId);
    }
    if (projects.length === 0 && !isLoading && !error) {
      fetchProjects();
    }
  }, [projectId, selectedProjectId, selectProject, projects.length, fetchProjects, isLoading, error]);

  const selectedProject = projects.find((p) => p.id === selectedProjectId);

  useEffect(() => {
    if (!selectedProjectId && !isLoading && !error && projects.length > 0) {
      router.push("/");
    }
  }, [selectedProjectId, isLoading, error, projects.length, router]);

  if (isLoading && !selectedProject) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" tip="Loading project details..." />
      </div>
    );
  }

  if (error && !selectedProject) {
    return <div className="text-center py-10 text-red-500">Error: {error}</div>;
  }

  if (!selectedProject) {
    return <div className="text-center py-10">Project not found or not selected. Redirecting...</div>;
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ padding: 0, background: '#fff' }}>
        <DashboardHeader projectName={selectedProject.name} />
      </Header>
      <Layout>
        <Sider width={250} style={{ background: '#fff' }}>
          <DashboardSidebar projectId={projectId} />
        </Sider>
        <Content style={{ margin: '24px 16px 0', overflow: 'initial' }}>
          <div style={{ padding: 24, background: '#fff', minHeight: 360 }}>
            {children}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
}